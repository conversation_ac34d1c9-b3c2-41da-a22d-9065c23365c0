{
    "terminal.integrated.profiles.linux": {
        "bash": {
            "path": "bash",
            "icon": "terminal-bash"
        },
        "zsh": {
            "path": "zsh"
        },
        "fish": {
            "path": "fish"
        },
        "tmux": {
            "path": "tmux",
            "icon": "terminal-tmux"
        },
        "pwsh": {
            "path": "pwsh",
            "icon": "terminal-powershell"
        }
    },
    "terminal.integrated.env.osx": {
        "JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home",
    },
    "terminal.integrated.inheritEnv": false,
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-1.8",
            "path": "/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home",
            "default": true
        }
    ],
    "java.compile.nullAnalysis.mode": "disabled"
}