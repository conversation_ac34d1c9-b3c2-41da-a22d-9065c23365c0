## 启动顺序cloud、exchange、market 其他随意
## 内存限制大小仅限于测试使用，运营项目根据需求设置

- nohup java -Xms512m -Xmx512m -jar cloud.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar exchange.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar market.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar exchange-api.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar ucenter-api.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar admin-api.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar wallet.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar wallet_udun.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar chat.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar otc-api.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx1024m -jar contract-swap-api.jar >/dev/null 2>&1 &


ps -ef | grep "512m"

- nohup java -Xms512m -Xmx512m -jar build/cloud.jar  >logs/debug-cloud.log 2>&1 &
- nohup java -Xms512m -Xmx512m -jar build/exchange.jar  >logs/debug-exchange.log 2>&1 &
- nohup java -Xms512m -Xmx512m -jar build/market.jar  >logs/debug-market.log 2>&1 &
- nohup java -Xms512m -Xmx512m -jar build/exchange-api.jar  >logs/debug-exchange-api.log 2>&1 &
- nohup java -Xms512m -Xmx512m -jar build/ucenter-api.jar  >logs/debug-ucenter-api.log 2>&1 &
- nohup java -Xms512m -Xmx512m -jar admin-api.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar wallet.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar wallet_udun.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar chat.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx512m -jar otc-api.jar  >/dev/null 2>&1 &
- nohup java -Xms512m -Xmx1024m -jar build/contract-swap-api.jar >logs/debug-swap-api.log 2>&1 &

### * wallet和wallet_udun只能部署一个
### * wallet需要配合wallet_rpc做本地钱包配置
### * wallet_udun为三方钱包U盾钱包，需要配合U盾钱包Key

# 快速启动
java -Xms512m -Xmx512m -jar cloud/target/cloud.jar
java -Xms512m -Xmx512m -jar exchange/target/exchange.jar
java -Xms512m -Xmx512m -jar market/target/market.jar
java -Xms512m -Xmx512m -jar exchange-api/target/exchange-api.jar
java -Xms512m -Xmx512m -jar ucenter-api/target/ucenter-api.jar
java -Xms512m -Xmx1024m -jar contract-swap-api/target/contract-swap-api.jar


docker run -it --rm bitnami/kafka:3 kafka-topics.sh --list  --bootstrap-server *************:9092
docker run -it --rm --link kafka:kafka bitnami/kafka:3 kafka-topics.sh --list  --bootstrap-server kafka:9092
docker run -it --rm --network=01_bizzan_framework_default bitnami/kafka:3 kafka-topics.sh --list  --bootstrap-server **********:9092



# 本地启动
bin/zookeeper-server-start.sh -daemon config/zookeeper.properties

./bin/kafka-server-start.sh -daemon config/server.properties


# 创建ropic

        ./bin/kafka-topics.sh --topic my-topic --create --bootstrap-server=localhost:9092


# topic 从头监听
bin/kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic my-topic  --from-beginning

# topic 从当前监听

bin/kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic my-topic

bin/kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic exchange-trade-plate 


# docker run -d --name=openresty -p 20080:80 -v `pwd`/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf -v `pwd`/h5:/html -v `pwd`/lualib:/usr/local/openresty/nginx/lualib openresty/openresty
# docker stop openresty && docker rm openresty
# docker restart openresty
# docker logs -f --tail=100 openresty


隐射nginx端口到公网+https+域名

# nohup ngrok http 20080 --log=stdout >> ngrok.log 2>&1 &