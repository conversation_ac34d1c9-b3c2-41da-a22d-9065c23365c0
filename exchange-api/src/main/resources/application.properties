server.port=6003
spring.application.name=exchange-api
server.context-path=/exchange
logging.level.*=debug

#Eureka
eureka.client.serviceUrl.defaultZone=@eureka.client.serviceUrl.defaultZone@
eureka.instance.instance-id=@eureka.instance.instance-id@
eureka.instance.prefer-ip-address=true

#Mysql
spring.datasource.url=@spring.datasource.url@
spring.datasource.username=@spring.datasource.username@
spring.datasource.password=@spring.datasource.password@
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initialSize=5
spring.datasource.druid.minIdle=5
spring.datasource.druid.maxActive=200
spring.datasource.druid.maxWait=60000
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.validationQuery=SELECT 1
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.druid.filters=stat,wall,log4j
spring.datasource.druid.testonborrow=false
spring.datasource.druid.testonreturn=false
spring.datasource.druid.testwhileidle=false

#Druid监控配置
# WebStatFilter配置，说明请参考Druid Wiki，配置_配置WebStatFilter
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.web-stat-filter.url-pattern=/*
spring.datasource.druid.web-stat-filter.exclusions=/druid/*,*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico
spring.datasource.druid.web-stat-filter.session-stat-enable=true
spring.datasource.druid.web-stat-filter.session-stat-max-count=10
spring.datasource.druid.web-stat-filter.principal-session-name=session_name
spring.datasource.druid.web-stat-filter.principal-cookie-name=cookie_name
spring.datasource.druid.web-stat-filter.profile-enable=
#StatViewServlet配置，说明请参考Druid Wiki，配置_StatViewServlet配置默认false
spring.datasource.druid.stat-view-servlet.enabled=true
#配置DruidStatViewServlet
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
#禁用HTML页面上的“Reset All”功能
spring.datasource.druid.stat-view-servlet.reset-enable=false
#Druid监控登录账户
spring.datasource.druid.stat-view-servlet.login-username=@druidlogin-username@
#Druid监控登录密码
spring.datasource.druid.stat-view-servlet.login-password=@druidlogin-password@
#IP白名单(没有配置或者为空，则允许所有访问)
spring.datasource.druid.stat-view-servlet.allow=
#IP黑名单 (存在共同时，deny优先于allow)
spring.datasource.druid.stat-view-servlet.deny=
#Spring监控配置，说明请参考Druid Github Wiki，配置_Druid和Spring关联监控配置
spring.datasource.druid.aop-patterns= com.lcf.service.*

#MongoDB
spring.data.mongodb.uri=@spring.data.mongodb.uri@

#Redis
spring.redis.host=@spring.redis.host@
spring.redis.port=@spring.redis.port@
## 连接池最大连接数（使用负值表示没有限制）
spring.redis.pool.max-active=300
## 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.pool.max-wait=60000
## 连接池中的最大空闲连接
spring.redis.pool.max-idle=100
## 连接池中的最小空闲连接
spring.redis.pool.min-idle=20
## 连接超时时间（毫秒）
spring.redis.timeout=30000
## 连接密码
# spring.redis.password=

#Kafka
spring.kafka.bootstrap-servers=@spring.kafka.bootstrap-servers@
# 重传次数
spring.kafka.producer.retries=0
# 每次批处理的大小
spring.kafka.producer.batch.size=256
#linger指定的时间等待更多的records出现
spring.kafka.producer.linger=1
# 缓存数据的内存大小
spring.kafka.producer.buffer.memory=1048576
spring.kafka.consumer.enable.auto.commit=false
spring.kafka.consumer.session.timeout=15000
spring.kafka.consumer.auto.commit.interval=100
spring.kafka.consumer.auto.offset.reset=earliest
spring.kafka.consumer.group.id=default-group
spring.kafka.consumer.concurrency=9
spring.kafka.consumer.maxPollRecordsConfig=50
spring.devtools.restart.enabled=true

#Mail Setting
spring.mail.host=@spring.mail.host@
spring.mail.port=@spring.mail.port@
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.username=@spring.mail.username@
spring.mail.password=@spring.mail.password@
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
#SYSTEM(发送邮件使用)
spark.system.work-id=1
spark.system.data-center-id=1
spark.system.host=@spark.system.host@
spark.system.name=@spark.system.name@
#接收系统通知的邮箱，多个用【,】分割
spark.system.admins=@spark.system.admins@
#通知短信接收手机，多个用【,】分割
spark.system.admin-phones=@spark.system.admin-phones@

#阿里云 邮件 & 短信 配置
aliyun.mail-sms.region=@aliyun.mail-sms.region@
aliyun.mail-sms.access-key-id=@aliyun.mail-sms.access-key-id@
aliyun.mail-sms.access-secret=@aliyun.mail-sms.access-secret@
aliyun.mail-sms.from-address=@aliyun.mail-sms.from-address@
aliyun.mail-sms.from-alias=@aliyun.mail-sms.from-alias@
aliyun.mail-sms.sms-sign=@aliyun.mail-sms.sms-sign@
aliyun.mail-sms.sms-template=@aliyun.mail-sms.sms-template@

#JPA
spring.jpa.show-sql=false
spring.data.jpa.repositories.enabled=true
#spring.jpa.hibernate.ddl-auto=update

#ES 配置文件
es.username=@es.username@
es.password=@es.password@
es.mine.index=@es.mine.index@
es.mine.type=@es.mine.type@
es.public.ip=@es.public.ip@
es.private.ip=@es.private.ip@
es.port=@es.port@