version: '3.8'

services:
  mysql:
    image: mysql:5.7.16
    container_name: mysql
    environment:
      MYSQL_ROOT_PASSWORD: dbadmin
      MYSQL_DATABASE: bizzan
      MYSQL_USER: admin
      MYSQL_PASSWORD: dbadmin
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./my.cnf:/etc/mysql/my.cnf  # 挂载自定义配置文件
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - bitrade_net

  # redis:
  #   image: redis:6.0
  #   container_name: redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - bitrade_net

  mongo:
    image: mongo:4.0
    container_name: mongo
    # environment:
    #   MONGO_INITDB_ROOT_USERNAME: root
    #   MONGO_INITDB_ROOT_PASSWORD: root
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    networks:
      - bitrade_net

  zookeeper:
    image: wurstmeister/zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    networks:
      - bitrade_net

  # kafka:
  #   image: wurstmeister/kafka:2.12-2.2.1
  #   container_name: kafka
  #   ports:
  #     - "9092:9092"
  #   environment:
  #     KAFKA_BROKER_ID: 1
  #     KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
  #     KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
  #     KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
  #     KAFKA_CREATE_TOPICS: "bitrade:1:1"
  #   depends_on:
  #     - zookeeper
  #   networks:
  #     - bitrade_net

volumes:
  mysql_data:
  # redis_data:
  mongo_data:

networks:
  bitrade_net:
    driver: bridge 

# docker-compose up -d    
# mysql -ubitrade -pbitrade -P3307 -h127.0.0.1
# docker-compose down mysql
# docker volume list
# docker volume rm ztuo-framework_mysql_data
# docker volume rm ztuo-framework_mongo_data
# ps -ef | grep "512m"