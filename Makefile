
clean:
	rm -rf build

package:
	mvn -DskipTests=true install package
	#cp cloud/target/cloud.jar build/
	#cp exchange/target/exchange.jar build/
	#cp market/target/market.jar build/
	#cp exchange-api/target/exchange-api.jar build/
	#cp ucenter-api/target/ucenter-api.jar build/
	#cp contract-swap-api/target/contract-swap-api.jar build/
	

build:
	mvn -DskipTests=true install package -P prod
	#mkdir build

up-cloud:
#	scp cloud/target/cloud.jar jump:jar/
#	ssh jump "scp jar/cloud.jar middle:."
#	ssh jump "ssh middle 'nohup java -Xms512m -Xmx512m -jar cloud.jar >> cloud.log 2>&1 &'"
	scp cloud/target/cloud.jar services:.
	ssh services 'nohup java -Xms512m -Xmx512m -jar cloud.jar >> cloud.log 2>&1 &'

conf-services:
	ssh jump "scp init.sh services:."
	ssh jump "ssh services sh init.sh"

up-exchange:
	scp exchange/target/exchange.jar jump:jar/
	ssh jump "scp jar/exchange.jar services:. && ssh services 'nohup java -Xms512m -Xmx512m -jar exchange.jar >> exchange.log 2>&1 &'"

up-market: build
	scp market/target/market.jar jump:jar/
	#ssh jump "scp jar/market.jar services:. && ssh services 'nohup java -Xms512m -Xmx512m -jar market.jar >> market.log 2>&1 &'"
	ssh jump "scp jar/market.jar services:. && ssh services 'sudo service market restart'"

up-ex-api: build
	scp exchange-api/target/exchange-api.jar jump:jar/
	#ssh jump "scp jar/exchange-api.jar services:. && ssh services 'nohup java -Xms512m -Xmx512m -jar exchange-api.jar >> exchange-api.log 2>&1 &'"
	ssh jump "scp jar/exchange-api.jar services:. && ssh services 'sudo systemctl restart exchange-api'"

user: build
	scp ucenter-api/target/ucenter-api.jar jump:jar/
	#ssh jump "scp jar/ucenter-api.jar services:. && ssh services 'nohup java -Xms512m -Xmx512m -jar ucenter-api.jar >> ucenter-api.log 2>&1 &'"
	ssh jump "scp jar/ucenter-api.jar services:. && ssh services 'sudo systemctl restart ucenter'"

up-swap: build
	scp contract-swap-api/target/contract-swap-api.jar jump:jar/
	#ssh jump "scp jar/contract-swap-api.jar services:. && ssh services 'nohup java -Xms512m -Xmx1024m -jar contract-swap-api.jar >> contract-swap-api.log 2>&1 &'"
	ssh jump "scp jar/contract-swap-api.jar services:. && ssh services 'sudo systemctl restart swap'"

up-admin: build
	scp admin/target/admin-api.jar jump:jar/
	#ssh jump "scp jar/admin-api.jar services:. && ssh services 'nohup java -Xms512m -Xmx512m -jar admin-api.jar >> admin-api.log 2>&1 &'"
	#ssh jump "scp jar/admin-api.jar services:. && ssh services 'nohup java -Xms512m -Xmx512m -jar admin-api.jar >> admin-api.log 2>&1 &'"
	ssh jump "scp jar/admin-api.jar services:. && ssh services 'sudo systemctl restart admin'"


up-wallet: build
	scp wallet/target/wallet.jar jump:jar/
	#ssh jump "scp jar/wallet.jar services:. && ssh services 'nohup java -Xms512m -Xmx512m -jar wallet.jar >> wallet.log 2>&1 &'"
	ssh jump "scp jar/wallet.jar services:. && ssh services 'sudo service wallet restart'"


up-sql:
	scp sql/jc_bizzan.sql jump:jar/


xx:
	ssh jump "scp services:/opt/swap.html jar/"
	scp jump:jar/swap.html logs/
